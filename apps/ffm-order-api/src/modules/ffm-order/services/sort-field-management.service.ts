import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  AddSortFieldDto,
  CreateSortFieldDto,
} from 'apps/ffm-order-api/src/dtos/sort-field-management.dto';
import { SortFieldManagement } from 'apps/ffm-order-api/src/entities/sort-field-management.entity';
import { FilterSortField } from 'apps/ffm-order-api/src/filters/sort-field-management.filter';
import { plainToInstance } from 'class-transformer';
import { orderConnection } from 'core/constants/database-connection.constant';
import { isEmpty, uniqBy } from 'lodash';
import { title } from 'process';
import { Repository } from 'typeorm';

@Injectable()
export class SortFieldService {
  constructor(
    @InjectRepository(SortFieldManagement, orderConnection)
    private sfmRepository: Repository<SortFieldManagement>,
  ) {}

  async createOrUpdate(
    data: CreateSortFieldDto,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<SortFieldManagement> {
    const { companyId, id } = request.user;
    const countryIds = headers['country-ids']?.split(',');

    if (!companyId) throw new UnauthorizedException();
    const sfm = await this.sfmRepository
      .createQueryBuilder('sfm')
      .andWhere('sfm.createdBy = :id', { id })
      .andWhere('sfm.countryId IN (:...countryIds)', { countryIds })
      .andWhere('sfm.companyId = :companyId', { companyId })
      .andWhere('sfm.type = :type', { type: data?.type })
      .getOne();

    if (sfm) {
      await this.sfmRepository.update({ id: sfm.id }, { fields: data?.fields }).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
    } else {
      const sfm = plainToInstance(SortFieldManagement, {
        ...data,
        companyId,
        countryId: countryIds[0],
        createdBy: id,
      });
      return await this.sfmRepository.save(sfm).catch(err => {
        if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
        return err;
      });
    }
    return sfm;
  }

  async apiSupport(
    data: AddSortFieldDto,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<SortFieldManagement[]> {

    const sfm = await this.sfmRepository
      .createQueryBuilder('sfm')
      .andWhere('sfm.type = :type', { type: data?.type })
      .getMany();

    if (!isEmpty(sfm)) {
      for (const item of sfm) {
        const newShown = {
          title: item?.fields?.shown?.title,
          values: uniqBy(
            [
              ...item?.fields?.shown?.values,
              { id: data?.fields, name: data?.fields, isHidden: true },
            ],
            'id',
          ),
        };
        item.fields = {
          freeze: item?.fields?.freeze,
          hidden: item?.fields?.hidden,
          shown: newShown,
        };
      }
    }
    await this.sfmRepository.save(sfm).catch(err => {
      if (err?.driverError) throw new BadRequestException(err?.driverError?.detail);
      return err;
    });
    return sfm;
  }

  async detail(
    filters: FilterSortField,
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<SortFieldManagement> {
    const { companyId, id } = request.user;
    const countryIds = headers['country-ids']?.split(',');

    if (!companyId) throw new UnauthorizedException();
    const query = await this.sfmRepository.createQueryBuilder('sfm')
      .andWhere('sfm.createdBy = :id', { id })
      .andWhere('sfm.companyId = :companyId', { companyId })
      .andWhere('sfm.type = :type', { type: filters?.type });
    if(countryIds?.length > 0) query.andWhere('sfm.countryId IN (:...countryIds)', { countryIds });
    const sfm = await query.getOne();
    return sfm;
  }
}
