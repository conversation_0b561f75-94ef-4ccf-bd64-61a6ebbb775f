import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ModuleFFM, SystemSettingOption } from 'apps/ffm-order-api/src/constants/system-setting.constants';
import { UpdateSystemSettingDto } from 'apps/ffm-order-api/src/dtos/system-setting.dto';
import { SystemSetting } from 'apps/ffm-order-api/src/entities/system-setting.entity';
import { orderConnection } from 'core/constants/database-connection.constant';
import { Repository } from 'typeorm';

@Injectable()
export class SystemSettingService {
  constructor(
    @InjectRepository(SystemSetting, orderConnection)
    private systemSettingRepository: Repository<SystemSetting>,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  async getSystemSetting(
    request: Record<string, any>,
    headers: Record<string, any>,
  ): Promise<SystemSetting[]> {
    const { companyId } = request.user;
    const countryIds = headers['country-ids']?.split(',');

    if (!companyId) throw new BadRequestException();
    const query = this.systemSettingRepository
      .createQueryBuilder('ss')
      .andWhere('ss.company_id = :companyId', { companyId });

    if(countryIds?.length > 0) query.andWhere('ss.country_id IN (:...countryIds)', { countryIds });
    return query.getMany();
  }

  async updateSystemSetting(
    body: UpdateSystemSettingDto,
    headers: Record<string, any>,
    request: Record<string, any>,
  ): Promise<SystemSetting[]> {
    const { companyId, id } = request.user;
    const countryIds = headers['country-ids']?.split(',');

    if (!companyId) throw new BadRequestException();

    const ids = body?.systemSettings?.map(x => x?.id);
    const systemSettings = await this.systemSettingRepository
      .createQueryBuilder('ss')
      .andWhere('ss.company_id = :companyId', { companyId })
      .andWhere('ss.country_id IN (:...countryIds)', { countryIds })
      .andWhere('ss.id IN (:...ids)', { ids })
      .getMany();
    const result = [];
    for (const data of body?.systemSettings) {
      let systemSetting = new SystemSetting();
      if (data?.module == ModuleFFM.Package && data?.option == SystemSettingOption.UseCustomPrefixPackage && !data?.prefix)
        throw new BadRequestException('Prefix is required');
      if (!data?.id) {
        systemSetting.module = data?.module;
        systemSetting.type = data?.type;
        systemSetting.companyId = companyId;
        systemSetting.countryId = countryIds[0];
        systemSetting.creatorId = id;
      } else {
        systemSetting = systemSettings.find(x => x.id == data?.id);
      }
      systemSetting.option = data?.option;
      systemSetting.lastUpdatedBy = id;
      systemSetting.prefix = data?.module == ModuleFFM.Package ? data?.prefix : null;
      result.push(systemSetting);
    }

    return await this.systemSettingRepository.save(result).catch(err => {
      if (err?.driverError) {
        throw new BadRequestException(err);
      }
      return err;
    });
  }
}
