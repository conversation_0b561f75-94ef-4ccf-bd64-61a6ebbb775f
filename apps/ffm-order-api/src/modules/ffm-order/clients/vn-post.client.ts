import { BadGatewayException, Injectable } from '@nestjs/common';
import {
  DistrictData,
  Districts,
  ProvinceData,
  Provinces,
  WardData,
  Wards,
} from 'apps/ffm-order-api/src/constants/vn-post-address';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import axios from 'axios';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import * as moment from 'moment-timezone';
import { Order } from '../../../entities/order.entity';
import { IWarehouse } from '../services/order-partners.service';
import { parseAddress, parseDataVNPOST } from '../utils/replace-address';
import { isNil, isNull } from 'lodash';

interface ISender {
  name: string;
  postcode: string;
  phone: string;
  mobile: string;
  email: string;
  countryCode: string;
  countryName: string;
  province: string;
  city: string;
  company: string;
  address?: string;
  address1?: string;
}

interface IVNPUser {
  userAccount: string;
  customerCode: string;
  contractCode: string;
  serviceCode: string;
  shippingMethod?: string;
  requiredUponDelivery?: string;
  orderCancellationFeeCharged?: number;
  exchangeOrReturn?: number;
}

export interface IVNPOrder {
  itemCode: string;
  originalItemCode: string;
  originalID: string;
  saleOrderCode: number;
  totalFee: number;
  mainFee: number;
  mainFeeBeforeTax: number;
  mainTax: number;
  vasFee: number;
  codAmount: number;
  carrierService: string;
}

@Injectable()
export class VNPostClient {
  private user: IVNPUser;
  private request = axios.create({
    baseURL: process.env.VN_POST_BASE_API,
    timeout: 50000
  });
  constructor(user: IVNPUser, redisService: RedisCacheService) {
    this.user = user;
  }

  public async createOrder(
    order: Order,
    warehouse: IWarehouse,
    extraService?: any,
  ): Promise<{ response?: IVNPOrder; message?: any; error?: any; code?: string }> {
    const token = process.env.VN_POST_TOKEN;
    const dataSender = {
      province: Provinces?.[warehouse?.provinceId]?.provinceCode ?? '',
      district: Districts?.[warehouse?.districtId]?.districtCode ?? '',
      ward: Wards?.[warehouse?.communeId]?.communeCode ?? '',
    };

    const dataRecipient = {
      province: Provinces?.[order?.recipientProvinceId]?.provinceCode ?? '',
      district: Districts?.[order?.recipientDistrictId]?.districtCode ?? '',
      ward: Wards?.[order?.recipientWardId]?.communeCode ?? '',
    };
    console.log("🚬 ~ VNPostClient ~ dataSender:", warehouse, dataSender, dataRecipient);

    if (
      !dataSender.province ||
      !dataSender.district ||
      !dataSender.ward ||
      !dataRecipient.province ||
      !dataRecipient.district ||
      !dataRecipient.ward
    ) {
      return {
        error: 'Address invalid',
        message: 'Address invalid',
        code: '500',
      };
    }

    let weight = 0;
    let subTotal = 0;
    // let quantity = 0;
    // const products = [];
    const title = [];
    for (let index = 0; index < order?.products.length; index++) {
      const prod: OrderProduct = order?.products[index];
      weight += prod?.weight ?? 0;
      subTotal += (prod?.quantity ?? 0) * (prod?.price ?? 0);
      const properties =
        prod?.productDetail?.properties?.length > 0
          ? `-${prod?.productDetail?.properties?.map((pp: any) => `${pp?.name ?? ''}`)?.join('-')}`
          : ``;
      // quantity += prod?.quantity ?? 0;
      title.push(
        `${prod?.productName} x ${prod?.quantity}`,
      );
    }

    const cod =
      (subTotal ?? 0) +
      (order?.shippingFee ?? 0) -
      (order?.discount ?? 0) +
      (order?.surcharge ?? 0) -
      (order?.paid ?? 0);

    const additionRequest = [];
    if (this.user.exchangeOrReturn) {
      if (extraService?.exchangeOrReturn) {
        additionRequest.push({
          code: 'GTG023',
          propValue: null,
        });
      }
    }
    if (this.user.orderCancellationFeeCharged) {
      if (extraService?.orderCancellationFeeCharged < 0) {
        additionRequest.push({
          code: 'GTG070',
          propValue: 'PROP0078:null',
        });
      } else {
        additionRequest.push({
          code: 'GTG070',
          propValue: `PROP0078:${extraService?.orderCancellationFeeCharged}`,
        });
      }
    }
    const deliveryInstruction = ["Cho khách kiểm tra hàng không bóc tem sản phẩm."];

    if (extraService?.orderCancellationFeeCharged)
      deliveryInstruction.push('Khách không nhận vui lòng thanh toán phí');
    if (extraService?.requiredUponDelivery == '1') deliveryInstruction.push('Không cho xem hàng');
    if (extraService?.requiredUponDelivery == '2') deliveryInstruction.push('Cho khách xem hàng');

    if (order?.waybillNote) deliveryInstruction.push(order?.waybillNote);

    const data = JSON.stringify({
      orderCreationStatus: 1,
      type: 'GUI',
      customerCode: this.user.customerCode ?? '',
      contractCode: this.user.contractCode ?? '',
      informationOrder: {
        senderName: warehouse?.name,
        senderPhone: warehouse?.phoneNumber,
        senderAddress: warehouse?.fullAddress,
        senderProvinceName: warehouse.addressSplit[2],
        senderProvinceCode: dataSender?.province,
        senderDistrictName: warehouse.addressSplit[1],
        senderDistrictCode: dataSender?.district,
        senderCommuneName: warehouse.addressSplit[0],
        senderCommuneCode: dataSender?.ward,
        receiverName: order?.recipientName,
        receiverAddress: parseAddress({
          address: order?.recipientAddress,
          province: order?.recipientProvince,
          district: order?.recipientDistrict,
          ward: order?.recipientWard,
        }),
        receiverProvinceName: order?.recipientProvince,
        receiverProvinceCode: dataRecipient?.province,
        receiverDistrictName: order?.recipientDistrict,
        receiverDistrictCode: dataRecipient?.district,
        receiverCommuneName: order?.recipientWard,
        receiverCommuneCode: dataRecipient?.ward,
        receiverPhone: order?.recipientPhone,
        serviceCode: this.user.serviceCode,
        saleOrderCode: order?.displayId,
        contentNote: title?.join('; ').substring(0, 200),
        weight: !isNil(order.totalWeight) ? order.totalWeight : weight,
        vehicle: 'BO',
        // sendType: this.user.customerCode == 'T016057250' ? '2' : '1',
        isBroken: '0',
        deliveryTime: 'N',
        // deliveryRequire: ['T016057250', 'T015868132']?.includes(this.user.customerCode) ? '2' : '1',
        deliveryInstruction: deliveryInstruction.join(', ').substring(0, 150),
        addonService:
          cod <= 0
            ? []
            : [
                {
                  code: 'GTG021',
                  propValue: `PROP0018:${cod}`,
                },
              ],
        additionRequest,
        sendType: extraService?.shippingMethod,
        deliveryRequire: extraService?.requiredUponDelivery,
      },
    });
    console.log('rod vn post data', data);
    try {
      const response = await this.request.post(`/customer-partner/CreateOrder`, data, {
        headers: {
          Token: token,
          'Content-Type': 'application/json',
        },
      });
      console.log('rod vn post response', response.data);
      if (response.data?.itemCode) {
        return {
          response: {
            itemCode: response.data?.itemCode,
            originalItemCode: response.data?.originalItemCode,
            originalID: response.data?.originalID,
            saleOrderCode: response.data?.saleOrderCode,
            totalFee: response.data?.totalFee,
            mainFee: response.data?.mainFee,
            mainFeeBeforeTax: response.data?.mainFeeBeforeTax,
            mainTax: response.data?.mainTax,
            vasFee: response.data?.vasFee,
            codAmount: response.data?.codAmount,
            carrierService: this.user.serviceCode,
          },
          code: '200',
        };
      } else {
        return {
          error: response.data,
          message: response.data?.message,
          code: '500',
        };
      }
    } catch (e) {
      console.log('rod error vn post', e?.response);
      return {
        error: e?.response?.data || {},
        message: e?.response?.data?.message ?? '',
        code: '500',
      };
    }
  }

  public async cancelOrder(
    order: OrderCarrier,
  ): Promise<{ response?: IVNPUser; error?: any; code?: string }> {
    const token = process.env.VN_POST_TOKEN;

    const data = JSON.stringify({
      OriginalId: order?.extraData?.originalID ?? order?.extraData?.originalItemCode,
    });

    console.log('vn post', order, data, token);

    try {
      const response = await this.request.post(`/customer-partner/orderCancel`, data, {
        headers: {
          Token: token,
          'Content-Type': 'application/json',
        },
      });

      const res = {
        response: response.data?.data,
        error: response?.data?.message ?? '',
        code: response?.data?.status ?? '',
      };

      console.log('canceled vn post', response.data);
      return res;
    } catch (e) {
      console.log('cancel error vn post', e?.response?.data);
      return {
        error: e?.response?.data || {},
        code: '500',
      };
    }
  }

  // async getAccessToken(): Promise<any> {
  //   const tokenKey = `vnp-token-${this.user.userAccount}`;
  //   const token = await this.redisService.get<string>(tokenKey);
  //   console.log(tokenKey, token);
  //   if (token) {
  //     return {
  //       code: 200,
  //       token,
  //     };
  //   }
  //   if (this.user.token) {
  //     await this.redisService.set(tokenKey, this.user.token, {
  //       ttl: moment()
  //         .add(365, 'days')
  //         ?.valueOf(),
  //     });
  //     return {
  //       error: '',
  //       code: 200,
  //       token: this.user.token,
  //     };
  //   } else {
  //     return {
  //       error: '',
  //       code: 500,
  //     };
  //   }
  // }
}
