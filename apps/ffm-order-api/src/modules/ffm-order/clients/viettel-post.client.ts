import { BadRequestException, Injectable } from '@nestjs/common';
import axios from 'axios';
import { Order } from '../../../entities/order.entity';
import { IWarehouse } from '../services/order-partners.service';
import * as Crypto from 'crypto';
import * as FormData from 'form-data';
import { find, isNil, isNull, reduce, takeRight, trim } from 'lodash';
import { ICountry } from '../services/pancake.service';
import { CountryCode } from 'core/constants/country_code';
import { OrderProduct } from 'apps/ffm-order-api/src/entities/order-product.entity';
import TranslateUtils from 'core/utils/TranslateUtils';
import { RedisCacheService } from 'core/cache/services/redisCache.service';
import * as moment from 'moment-timezone';
import { OrderCarrier } from 'apps/ffm-order-api/src/entities/order-carrier.entity';
import { parseAddress } from '../utils/replace-address';
import cheerio from 'cheerio';

interface BaseResponse {
  response?: any;
  error?: any;
}

export interface INimBusTracking {
  id: string;
  order_id: string;
  order_number: string;
  created: string;
  awb_number: string;
  rto_awb: string;
  courier_id: string;
  warehouse_id: string;
  rto_warehouse_id: string;
  status: string;
  rto_status: string;
  shipment_info: string;
}

interface ISender {
  name: string;
  postcode: string;
  phone: string;
  mobile: string;
  email: string;
  countryCode: string;
  countryName: string;
  province: string;
  city: string;
  company: string;
  address?: string;
  address1?: string;
}

interface IVTPUser {
  userAccount: string;
  password: string;
  soCodes?: string[];
  exchangeOrReturn?: string;
  partialDelivery?: string;
}

export interface IVTPOrder {
  ORDER_NUMBER: string;
  MONEY_COLLECTION: number;
  MONEY_TOTAL: number;
}

@Injectable()
export class ViettelPostClient {
  private user: IVTPUser;
  private request = axios.create({
    baseURL: process.env.VIETTEL_POST_BASE_API,
  });
  private redisService: RedisCacheService;
  constructor(user: IVTPUser, redisService: RedisCacheService) {
    this.user = user;
    this.redisService = redisService;
  }

  public async createOrder(
    order: Order,
    warehouse: IWarehouse,
    extraService?: any,
  ): Promise<{ response?: IVTPOrder; message?: any; error?: any; code?: string }> {
    const token = await this.getAccessToken();
    const countrySender: ICountry = find(CountryCode, {
      dial_code: `+${warehouse?.countryCode}`,
    });
    const countryReceiver: ICountry = find(CountryCode, {
      dial_code: `+${order?.recipientCountryId}`,
    });

    let weight = 0;
    let subTotal = 0;
    let quantity = 0;
    const products = [];
    const title = [];

    order?.products?.forEach((prod: OrderProduct) => {
      weight += prod?.weight ?? 0;
      subTotal += (prod?.quantity ?? 0) * (prod?.price ?? 0);
      quantity += prod?.quantity ?? 0;
      products.push({
        PRODUCT_NAME: prod?.productName,
        PRODUCT_QUANTITY: prod?.quantity,
        PRODUCT_PRICE: prod?.price,
        PRODUCT_WEIGHT: prod?.weight,
      });
      title.push(
        `"${prod?.quantity} x ${prod?.productName}"`,
      );
    });

    const cod =
      (subTotal ?? 0) +
      (order?.shippingFee ?? 0) -
      (order?.discount ?? 0) +
      (order?.surcharge ?? 0) -
      (order?.paid ?? 0);

    order.totalPrice = Number(order.totalPrice.toFixed(2));

    let ORDER_SERVICE_ADD = null;
    if (extraService.partialDelivery && extraService.exchangeOrReturn) {
      throw new BadRequestException(`Chỉ được chọn 1 trong 2 dịch vụ 'Giao một phần' / 'Đổi hàng'`);
    } else if (extraService?.partialDelivery) {
      if (this.user.partialDelivery) ORDER_SERVICE_ADD = 'GG1P';
    } else if (extraService?.exchangeOrReturn) {
      if (this.user.exchangeOrReturn) ORDER_SERVICE_ADD = 'GGDH';
    }
    const carrierService = 'ECOD';

    const data = JSON.stringify({
      ORDER_NUMBER: order?.displayId,
      SENDER_FULLNAME: warehouse?.name,
      SENDER_ADDRESS: warehouse?.fullAddress,
      SENDER_PHONE: warehouse?.phoneNumber,
      RECEIVER_FULLNAME: order?.recipientName,
      RECEIVER_ADDRESS: parseAddress({
        address: order?.recipientAddress,
        province: order?.recipientProvince,
        district: order?.recipientDistrict,
        ward: order?.recipientWard,
      }),
      RECEIVER_PHONE: order?.recipientPhone,
      PRODUCT_NAME: title?.join(',\n').substring(0, 200),
      PRODUCT_DESCRIPTION: '',
      PRODUCT_QUANTITY: quantity,
      PRODUCT_PRICE: subTotal,
      PRODUCT_WEIGHT: !isNil(order.totalWeight) ? order.totalWeight : weight,
      ORDER_PAYMENT: 3,
      ORDER_SERVICE: carrierService,
      PRODUCT_TYPE: 'HH',
      // ORDER_SERVICE_ADD: cod > 0 ? !isNull(ORDER_SERVICE_ADD) ? `${ORDER_SERVICE_ADD},XMG` : 'XMG' : ORDER_SERVICE_ADD,
      ORDER_SERVICE_ADD,
      ORDER_NOTE: 'Cho khách kiểm tra hàng không bóc tem sản phẩm.',
      MONEY_COLLECTION: cod,
      // EXTRA_MONEY: cod > 0 ? 30000 : 0,
      CHECK_UNIQUE: true,
      LIST_ITEM: products,
    });

    console.log('rod viettel post body', data);

    try {
      const response = await this.request.post(`/order/createOrderNlp`, data, {
        headers: {
          Token: token,
          'Content-Type': 'application/json',
          Cookie: 'SERVERID=A',
        },
      });

      const res = {
        response: {
          ...response.data?.data,
          carrierService: carrierService
        },
        error: response?.data?.message ? response?.data?.message : response?.data?.error ?? '',
        code: response?.data?.status ?? '',
        message: response?.data?.message ?? '',
      };
      if (!res?.error || response?.data?.status == 200) delete res.error;
      console.log('rod viettel post', res, response);
      return res;
    } catch (e) {
      console.log('rod error viettel post', e);
      return {
        error: e?.response?.data || {},
        code: '500',
      };
    }
  }

  public async cancelOrder(
    order: OrderCarrier,
  ): Promise<{ response?: IVTPOrder; error?: any; code?: string }> {
    const token = await this.getAccessToken();

    const data = JSON.stringify({
      ORDER_NUMBER: order?.waybillNumber,
      TYPE: 4,
      NOTE: '',
    });

    console.log('viettel post', data, token);

    try {
      const response = await this.request.post(`/order/UpdateOrder`, data, {
        headers: {
          Token: token,
          'Content-Type': 'application/json',
          Cookie: 'SERVERID=A',
        },
      });

      const res = {
        response: response.data?.data,
        error: response?.data?.message ?? '',
        code: response?.data?.status ?? '',
      };
      if (!res?.error || response?.data?.status == 200) delete res.error;
      console.log('canceled viettel post', res);
      return res;
    } catch (e) {
      console.log('cancel error viettel post', e);
      return {
        error: e?.response?.data || {},
        code: '500',
      };
    }
  }

  async getAccessToken(): Promise<string> {
    const tokenKey = `vtp-token-${this.user.userAccount}`;
    const token = await this.redisService.get<string>(tokenKey);
    if (token) {
      return token;
    }
    try {
      const response = await this.request.post(
        `/user/Login`,
        {
          USERNAME: this.user.userAccount,
          PASSWORD: this.user.password,
        },
        {
          headers: {
            // Token: this.user.token,
            'Content-Type': 'application/json',
            // Cookie: 'SERVERID=A',
          },
        },
      );
      if (response?.data?.status == 200 && response.data?.data?.token) {
        const res = await this.request.post(
          `/user/ownerconnect`,
          {
            USERNAME: this.user.userAccount,
            PASSWORD: this.user.password,
          },
          {
            headers: {
              Token: response.data?.data?.token,
              'Content-Type': 'application/json',
              Cookie: 'SERVERID=A; SERVERID=D',
            },
          },
        );
        console.log('updated vtp token', tokenKey, res.data?.data?.token, res.data.expired);
        if (res.data?.data?.token) {
          await this.redisService.set(tokenKey, res.data?.data?.token, {
            ttl: moment()
              .add(15, 'days')
              ?.valueOf(),
          });
          return response.data?.data?.token;
        } else throw new BadRequestException();
      } else throw new BadRequestException();
    } catch (e) {
      console.log('update vtp token error', e);
      throw e;
    }
  }

  public async validAccount(): Promise<any> {
    const data = JSON.stringify({
      USERNAME: this.user.userAccount,
      PASSWORD: this.user.password,
    });

    try {
      const response = await this.request.post(`/user/Login`, data, {
        headers: {
          // Token: this.user.token,
          'Content-Type': 'application/json',
          // Cookie: 'SERVERID=A',
        },
      });

      const res = {
        response: response.data?.data,
        error: response?.data?.message ?? '',
        code: response?.data?.status ?? '',
      };
      if (!res?.error || response?.data?.status == 200) delete res.error;
      console.log('check viettel post', res);
      return res;
    } catch (e) {
      console.log('check error viettel post', e);
      return {
        error: e?.response?.data || {},
        code: '500',
      };
    }
  }

  public async getDataWaybill(): Promise<Record<string, any>> {
    const resp: Record<string, any> = await this.printWaybill();
    console.log('vtp', resp);

    if (resp?.code == '200' && resp?.url) {
      const res = await axios.get(
        `https://digitalize.viettelpost.vn/DigitalizePrint/report.do?type=2&bill=${resp?.url}&showPostage=0&printCopy=1&showPhone=false&showProductContent=true`,
        {
          headers: {
            Accept:
              'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
          },
        },
      );
      const html = res.data;
      const $ = cheerio.load(html);
      let data = [];
      $('.mainPrints div:contains("Kg")').map((i, el) => {
        if (i == 0)
          data = takeRight(
            $(el)
              .text()
              .toString()
              .replace(/[\r\n]+/gm, ' ')
              .split('/'),
            3,
          );
      });
      return {
        code: 200,
        data: data?.map((it: any) => trim(it)),
      };
    }
    return {
      code: 500,
    };
  }

  public async printWaybill(): Promise<Record<string, any>> {
    const token = await this.getAccessToken();
    const data = JSON.stringify({
      EXPIRY_TIME: moment()
        ?.add(5, 'minutes')
        ?.valueOf(),
      ORDER_ARRAY: this.user?.soCodes,
    });

    try {
      const response = await this.request.post(`/order/printing-code`, data, {
        headers: {
          accept: '*/*',
          Token: token,
          'Content-Type': 'application/json',
          Cookie: 'SERVERID=F',
        },
      });
      console.log(
        'viettel post',
        response?.data,
        `https://digitalize.viettelpost.vn/DigitalizePrint/report.do?type=2&bill=${response?.data?.message}&showPostage=0&printCopy=1&showPhone=false&showProductContent=true`,
      );
      if (response?.data?.status == 200) {
        return {
          url: response?.data?.message,
          code: '200',
        };
      } else {
        return {
          error: response?.data?.message,
          code: '500',
        };
      }
    } catch (e) {
      console.log('check error viettel post', e);
      return {
        error: e?.response?.data || {},
        code: '500',
      };
    }
  }
}
