import { Field, Int, ObjectType } from '@nestjs/graphql';
import { Expose } from 'class-transformer';
import GraphQLTypeJSON from 'graphql-type-json';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Order } from './order.entity';

@Entity('order_products')
@ObjectType()
export class OrderProduct {
  @PrimaryGeneratedColumn({
    name: 'id',
  })
  @Expose()
  @Field(() => Int)
  id: number;

  @Column({
    name: 'order_id',
    type: 'int',
  })
  @Expose()
  @Index()
  @Field(() => Int)
  orderId: number;

  @Column({
    name: 'product_id',
    type: 'int',
  })
  @Expose()
  @Field(() => Int)
  productId: number;

  @Column({
    name: 'product_detail',
    type: 'jsonb',
  })
  @Expose()
  @Field(() => GraphQLTypeJSON)
  productDetail: Record<string, any>;

  @Column({ type: 'double precision' })
  @Expose()
  @Field(() => Int)
  quantity: number;

  @Column({ type: 'double precision' })
  @Expose()
  @Field(() => Int)
  price: number;

  @Column({ type: 'double precision', nullable: true })
  @Expose()
  @Field(() => Int, { nullable: true })
  editedPrice?: number;

  @Column({ type: 'int', nullable: true })
  @Expose()
  @Field(() => Int, { nullable: true })
  priceEditedBy?: number;

  @Column({ type: 'int', nullable: true })
  @Expose()
  @Field(() => Int, { nullable: true })
  priceAcceptedBy?: number;

  @Column({ name: 'index_in_order', type: 'int', default: 0, nullable: true })
  @Expose()
  @Field(() => Int)
  indexInOrder: number;

  @ManyToOne(
    () => Order,
    order => order.products,
  )
  @JoinColumn({
    name: 'order_id',
  })
  @Expose()
  order: Order;
}
