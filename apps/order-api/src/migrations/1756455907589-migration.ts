import { MigrationInterface, QueryRunner } from "typeorm";

export class migration1756455907589 implements MigrationInterface {
  name = 'migration1756455907589';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "order_products"
      ADD COLUMN "index_in_order" integer DEFAULT 0
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "order_products"
      DROP COLUMN "index_in_order"
    `);
  }
}
