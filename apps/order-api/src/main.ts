import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TransformInterceptor } from 'core/interceptors';
import { AppModule } from './app.module';
import 'core/extensions';
import { router } from 'bull-board';
import * as basicAuth from 'express-basic-auth';
import { Logger } from 'core/interceptors/logger.interceptors';
import { RedisIoAdapter } from './modules/socket/adapters/redis.adapter';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.enableCors({
    origin: [/https?:\/\/.*$/],
    credentials: true,
    allowedHeaders: '*',
  });
  app.useGlobalPipes(new ValidationPipe({ transform: true, whitelist: true }));
  app.useGlobalInterceptors(
    new TransformInterceptor(),
    process.env.SEQ_KEY ? new Logger('order-api') : undefined,
  );
  app.use(
    '/queues',
    basicAuth({
      users: { admin: process.env.BULL_PASSWORD },
      challenge: true,
    }),
    (req, res, next) => {
      req.proxyUrl = (process.env.API_SUBPATH || '') + '/queues';
      next();
    },
    router,
  );

  // const redisIoAdapter = new RedisIoAdapter(app);
  // await redisIoAdapter.connectToRedis();
  // app.useWebSocketAdapter(redisIoAdapter);

  const options = new DocumentBuilder()
    .setTitle('Node Backend')
    .setDescription('Backend writing in Node.js')
    .setVersion('0.1.0')
    .addServer(process.env.BASE_API_URL || '')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, options);
  SwaggerModule.setup('swagger', app, document);
  try {
    await app.startAllMicroservices();
    await app.listen(
      3001
    );
  } catch (e) {
    console.log('e', e);
    await app.close();
  }
}

bootstrap();
